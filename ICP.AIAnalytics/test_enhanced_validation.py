#!/usr/bin/env python3
"""
Test script for the enhanced validation API that supports upload and validation in a single call.
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:8000"  # Update with your API URL
REPORT_ID = "181493"
BEARER_TOKEN = "your_bearer_token_here"  # Update with actual token
CLIENT_CODE = "ONLINEMISC"

def test_traditional_validation():
    """Test traditional validation of existing report."""
    print("=== Testing Traditional Validation ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "direct_client_code": CLIENT_CODE,
        "async_processing": False
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    response = requests.post(url, json=payload)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Success! Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Processing Time: {result.get('processing_time', 'N/A')}s")
        print(f"Upload Info: {'Yes' if 'upload_info' in result else 'No'}")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_upload_and_validate_sync():
    """Test upload and validate in single call (synchronous)."""
    print("=== Testing Upload & Validate (Synchronous) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,
        "direct_client_code": CLIENT_CODE,
        "async_processing": False
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Total Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success! Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Processing Time: {result.get('processing_time', 'N/A')}s")
        
        # Check upload info
        upload_info = result.get('upload_info')
        if upload_info:
            print("Upload Info:")
            print(f"  - File ID: {upload_info.get('file_id')}")
            print(f"  - Filename: {upload_info.get('filename')}")
            print(f"  - Source: {upload_info.get('source')}")
            print(f"  - File Size: {upload_info.get('file_size')} bytes")
        else:
            print("No upload info (report already existed)")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_upload_and_validate_async():
    """Test upload and validate in single call (asynchronous)."""
    print("=== Testing Upload & Validate (Asynchronous) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,
        "direct_client_code": CLIENT_CODE,
        "async_processing": True
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    response = requests.post(url, json=payload)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        validation_id = result.get('validation_id')
        print(f"Validation started! ID: {validation_id}")
        print(f"Status: {result.get('status')}")
        print(f"Message: {result.get('message')}")
        
        # Check upload info
        upload_info = result.get('upload_info')
        if upload_info:
            print("Upload Info:")
            print(f"  - File ID: {upload_info.get('file_id')}")
            print(f"  - Filename: {upload_info.get('filename')}")
            print(f"  - Source: {upload_info.get('source')}")
        
        # Poll for completion
        status_url = f"{API_BASE_URL}/api/v1/validate/status/{validation_id}"
        print(f"\nPolling status at: {status_url}")
        
        max_attempts = 20
        for attempt in range(max_attempts):
            time.sleep(2)
            status_response = requests.get(status_url)
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                current_status = status_data.get('status')
                print(f"Attempt {attempt + 1}: Status = {current_status}")
                
                if current_status in ['completed', 'failed']:
                    if current_status == 'completed':
                        result_data = status_data.get('result', {})
                        print(f"✅ Validation completed!")
                        print(f"Total Questions: {result_data.get('total_questions')}")
                        print(f"Processing Time: {result_data.get('processing_time', 'N/A')}s")
                    else:
                        print(f"❌ Validation failed: {status_data.get('error')}")
                    break
            else:
                print(f"Error checking status: {status_response.text}")
                break
        else:
            print("⏰ Timeout waiting for validation to complete")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_upload_and_validate_with_order_details():
    """Test upload and validate with order details API for client code."""
    print("=== Testing Upload & Validate with Order Details ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,
        "enable_client_filtering": True,
        "order_details_params": {
            "csr_id": "12345",
            "copy": "1", 
            "version": "2"
            # Note: bearer_token is inherited from parent
        },
        "async_processing": False
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Total Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success! Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Client Code from API: {result.get('order_client_code', 'N/A')}")
        
        # Check upload info
        upload_info = result.get('upload_info')
        if upload_info:
            print("Upload Info:")
            print(f"  - File ID: {upload_info.get('file_id')}")
            print(f"  - Source: {upload_info.get('source')}")
    else:
        print(f"Error: {response.text}")
    
    print()

def main():
    """Run all tests."""
    print("Enhanced Validation API Test Suite")
    print("=" * 50)
    print("Testing simplified bearer_token usage!")
    print()
    
    # Test 1: Traditional validation (for comparison)
    try:
        test_traditional_validation()
    except Exception as e:
        print(f"Traditional validation test failed: {e}\n")
    
    # Test 2: Upload & Validate (Synchronous)
    try:
        test_upload_and_validate_sync()
    except Exception as e:
        print(f"Upload & validate sync test failed: {e}\n")
    
    # Test 3: Upload & Validate with Order Details
    try:
        test_upload_and_validate_with_order_details()
    except Exception as e:
        print(f"Upload & validate with order details test failed: {e}\n")
    
    # Test 4: Upload & Validate (Asynchronous)
    try:
        test_upload_and_validate_async()
    except Exception as e:
        print(f"Upload & validate async test failed: {e}\n")
    
    print("Test suite completed!")
    print("\n" + "=" * 50)
    print("✅ Key Improvement: Single bearer_token for all operations!")
    print("   - Upload from external API")
    print("   - Order details API calls")
    print("   - No more redundant token fields")

if __name__ == "__main__":
    main() 