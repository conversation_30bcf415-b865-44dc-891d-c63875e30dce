import chromadb
from chromadb.config import Settings as ChromaSettings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from typing import List, Dict, Any, Optional
import uuid
import json
from pathlib import Path

from app.core.config import settings


class VectorStore:
    def __init__(self):
        # Initialize ChromaDB client based on configuration mode
        if settings.CHROMADB_MODE == "server":
            print(f"Connecting to ChromaDB server at {settings.CHROMADB_HOST}:{settings.CHROMADB_PORT}")
            self.client = chromadb.HttpClient(
                host=settings.CHROMADB_HOST,
                port=settings.CHROMADB_PORT,
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=False  # Production safety
                )
            )
            self.mode = "server"
        else:
            print(f"Using embedded ChromaDB at {settings.CHROMADB_PATH}")
            self.client = chromadb.PersistentClient(
                path=settings.CHROMADB_PATH,
                settings=ChromaSettings(anonymized_telemetry=False)
            )
            self.mode = "embedded"
        
        # Initialize embeddings
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY
        ) if settings.OPENAI_API_KEY else None
        
        # Initialize text splitter with character-based chunking
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP,
            length_function=len,
        )
        
    def _get_or_create_collection(self, collection_name: str):
        """Get or create a ChromaDB collection."""
        try:
            return self.client.get_collection(collection_name)
        except:
            return self.client.create_collection(
                name=collection_name,
                metadata={"hnsw:space": "cosine"}
            )

    async def add_report_to_vector_store(self, file_id: str, report_data: Dict[str, Any]) -> str:
        """Add XML report content to vector store for RAG."""
        try:
            collection_name = f"report_{file_id}"
            
            # Delete existing collection if it exists to avoid ID conflicts
            try:
                existing_collection = self.client.get_collection(collection_name)
                if existing_collection:
                    self.client.delete_collection(collection_name)
                    print(f"Deleted existing collection: {collection_name}")
            except:
                pass  # Collection doesn't exist, which is fine
                
            collection = self._get_or_create_collection(collection_name)
            
            # Extract text content from report
            text_content = report_data.get("text_content", [])
            
            documents = []
            metadatas = []
            ids = []
            unique_id_counter = 0
            
            for item_index, item in enumerate(text_content):
                if item.get("text"):
                    # Create document chunks
                    text = f"Path: {item['path']}\nTag: {item['tag']}\nContent: {item['text']}"
                    
                    # Split long text into chunks
                    chunks = self.text_splitter.split_text(text)
                    
                    for chunk_index, chunk in enumerate(chunks):
                        # Generate unique ID using both item index and chunk index
                        doc_id = f"{file_id}_item_{item_index}_chunk_{chunk_index}_{unique_id_counter}"
                        unique_id_counter += 1
                        
                        documents.append(chunk)
                        metadatas.append({
                            "file_id": file_id,
                            "path": item["path"],
                            "tag": item["tag"],
                            "chunk_index": chunk_index,
                            "item_index": item_index,
                            "attributes": json.dumps(item.get("attributes", {}))
                        })
                        ids.append(doc_id)
            
            if documents:
                # Verify all IDs are unique before adding
                if len(set(ids)) != len(ids):
                    raise Exception(f"Generated non-unique IDs. Total: {len(ids)}, Unique: {len(set(ids))}")
                
                # Generate embeddings if OpenAI API key is available
                embeddings = None
                if self.embeddings:
                    embeddings = await self._generate_embeddings(documents)
                
                # Add to collection
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids,
                    embeddings=embeddings
                )
                
                print(f"Added {len(documents)} documents to collection {collection_name} ({self.mode} mode)")
            
            return collection_name
            
        except Exception as e:
            raise Exception(f"Error adding report to vector store: {str(e)}")

    async def search_relevant_content(self, file_id: str, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant content in the vector store."""
        try:
            collection_name = f"report_{file_id}"
            collection = self._get_or_create_collection(collection_name)
            
            # Generate query embedding if possible
            query_embedding = None
            if self.embeddings:
                query_embedding = await self._generate_embeddings([query])
                query_embedding = query_embedding[0] if query_embedding else None
            
            # Search in vector store
            if query_embedding:
                results = collection.query(
                    query_embeddings=[query_embedding],
                    n_results=n_results
                )
            else:
                # Fallback to text-based search
                results = collection.query(
                    query_texts=[query],
                    n_results=n_results
                )
            
            # Format results
            relevant_content = []
            if results["documents"]:
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results["metadatas"] else {}
                    distance = results["distances"][0][i] if results["distances"] else 0
                    
                    relevant_content.append({
                        "content": doc,
                        "metadata": metadata,
                        "relevance_score": 1 - distance,  # Convert distance to similarity
                        "path": metadata.get("path", ""),
                        "tag": metadata.get("tag", "")
                    })
            
            return relevant_content
            
        except Exception as e:
            raise Exception(f"Error searching vector store: {str(e)}")

    async def add_questions_to_vector_store(self, file_id: str, questions: List[Dict[str, Any]]) -> str:
        """Add questions to vector store for better matching."""
        try:
            collection_name = f"questions_{file_id}"
            collection = self._get_or_create_collection(collection_name)
            
            documents = []
            metadatas = []
            ids = []
            
            for question in questions:
                question_text = question.get("question", "")
                if question_text:
                    documents.append(question_text)
                    metadatas.append({
                        "file_id": file_id,
                        "question_id": question.get("id", ""),
                        "category": question.get("category", ""),
                        "priority": question.get("priority", "medium"),
                        "expected_format": question.get("expected_format", "")
                    })
                    ids.append(question.get("id", str(uuid.uuid4())))
            
            if documents:
                # Generate embeddings if possible
                embeddings = None
                if self.embeddings:
                    embeddings = await self._generate_embeddings(documents)
                
                # Add to collection
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids,
                    embeddings=embeddings
                )
            
            return collection_name
            
        except Exception as e:
            raise Exception(f"Error adding questions to vector store: {str(e)}")

    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get information about a collection."""
        try:
            collection = self.client.get_collection(collection_name)
            count = collection.count()
            return {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata,
                "mode": self.mode
            }
        except Exception as e:
            return {"error": str(e)}

    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection."""
        try:
            self.client.delete_collection(collection_name)
            return True
        except Exception:
            return False

    async def _generate_embeddings(self, texts: List[str]) -> Optional[List[List[float]]]:
        """Generate embeddings for texts using OpenAI."""
        try:
            if not self.embeddings:
                return None
            
            embeddings = []
            # Process in batches to avoid rate limits
            batch_size = 50
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_embeddings = self.embeddings.embed_documents(batch)
                embeddings.extend(batch_embeddings)
            
            return embeddings
            
        except Exception as e:
            print(f"Error generating embeddings: {str(e)}")
            return None

    async def list_collections(self) -> List[str]:
        """List all collections in the vector store."""
        try:
            collections = self.client.list_collections()
            return [col.name for col in collections]
        except Exception:
            return []

    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information."""
        try:
            collections = self.client.list_collections()
            version = self.client.get_version()
            return {
                "mode": self.mode,
                "host": getattr(settings, 'CHROMADB_HOST', 'localhost') if self.mode == "server" else "embedded",
                "port": getattr(settings, 'CHROMADB_PORT', 8001) if self.mode == "server" else None,
                "path": getattr(settings, 'CHROMADB_PATH', './data/vector_db') if self.mode == "embedded" else None,
                "version": version,
                "collections_count": len(collections),
                "status": "connected"
            }
        except Exception as e:
            return {
                "mode": self.mode,
                "status": "error",
                "error": str(e)
            } 
