#!/usr/bin/env python3
"""
Test script for the single bearer token implementation of the enhanced validation API.
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:8000"  # Update with your API URL
REPORT_ID = "181493"
BEARER_TOKEN = "your_bearer_token_here"  # Update with actual token
CLIENT_CODE = "ONLINEMISC"

def test_traditional_validation():
    """Test traditional validation of existing report (no bearer token)."""
    print("=== Testing Traditional Validation (No Bearer Token) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "direct_client_code": CLIENT_CODE
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    response = requests.post(url, json=payload)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Success! Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Upload Info: {'Yes' if 'upload_info' in result else 'No'}")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_upload_and_validate_with_direct_client():
    """Test upload and validate with single bearer token and direct client code."""
    print("=== Testing Upload & Validate (Single Bearer Token + Direct Client) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,  # Single bearer token
        "direct_client_code": CLIENT_CODE
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Total Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success! Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")

        # Check upload info
        upload_info = result.get('upload_info')
        if upload_info:
            print("Upload Info:")
            print(f"  - File ID: {upload_info.get('file_id')}")
            print(f"  - Source: {upload_info.get('source')}")
            print(f"  - File Size: {upload_info.get('file_size')} bytes")
        else:
            print("No upload (report already existed)")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_upload_and_validate_with_order_details():
    """Test upload and validate with single bearer token and order details API."""
    print("=== Testing Upload & Validate (Single Bearer Token + Order Details) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,  # ✅ Single bearer token for BOTH upload AND order details
        "enable_client_filtering": True,
        "order_details_params": {      # ✅ NO bearer_token here anymore
            "csr_id": "12345",
            "copy": "1", 
            "version": "2"
        }
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    print("🔑 Note: Single bearer_token used for BOTH upload and order details API")
    
    start_time = time.time()
    response = requests.post(url, json=payload)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Total Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Success! Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Client Code: {result.get('order_client_code', 'N/A')}")
        
        # Check upload info
        upload_info = result.get('upload_info')
        if upload_info:
            print("📤 Upload Info:")
            print(f"  - File ID: {upload_info.get('file_id')}")
            print(f"  - Source: {upload_info.get('source')}")
    else:
        print(f"❌ Error: {response.text}")
    
    print()

def test_async_upload_and_validate():
    """Test async upload and validate with single bearer token."""
    print("=== Testing Async Upload & Validate (Single Bearer Token) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,  # ✅ Single bearer token
        "direct_client_code": CLIENT_CODE,
        "async_processing": True  # Query parameter alternative
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    response = requests.post(url, json=payload, params={"async_processing": True})
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        validation_id = result.get('validation_id')
        print(f"✅ Async validation started! ID: {validation_id}")
        print(f"Status: {result.get('status')}")
        print(f"Message: {result.get('message')}")
        
        # Check upload info
        upload_info = result.get('upload_info')
        if upload_info:
            print("📤 Upload completed during async start:")
            print(f"  - File ID: {upload_info.get('file_id')}")
            print(f"  - Source: {upload_info.get('source')}")
        
        # Poll for completion
        status_url = f"{API_BASE_URL}/api/v1/validate/status/{validation_id}"
        print(f"\n🔄 Polling status...")
        
        for attempt in range(10):  # Reduced attempts for demo
            time.sleep(2)
            status_response = requests.get(status_url)
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                current_status = status_data.get('status')
                print(f"  Attempt {attempt + 1}: {current_status}")
                
                if current_status in ['completed', 'failed']:
                    if current_status == 'completed':
                        print(f"  ✅ Async validation completed!")
                    else:
                        print(f"  ❌ Async validation failed: {status_data.get('error')}")
                    break
            else:
                print(f"  ❌ Error checking status: {status_response.text}")
                break
        else:
            print("  ⏰ Stopped polling (demo timeout)")
    else:
        print(f"❌ Error: {response.text}")
    
    print()

def compare_old_vs_new_request():
    """Show the difference between old and new request structures."""
    print("=== Request Structure Comparison ===")
    
    print("❌ OLD (Confusing - Duplicate Bearer Tokens):")
    old_request = {
        "report_id": "181493",
        "bearer_token": "token_for_upload",  # ❌ For upload
        "order_details_params": {
            "csr_id": "12345",
            "copy": "1",
            "version": "2",
            "bearer_token": "token_for_order_details"  # ❌ For order details API
        }
    }
    print(json.dumps(old_request, indent=2))
    
    print("\n✅ NEW (Clean - Single Bearer Token):")
    new_request = {
        "report_id": "181493",
        "bearer_token": "single_token_for_all",  # ✅ For ALL operations
        "order_details_params": {
            "csr_id": "12345",
            "copy": "1",
            "version": "2"
            # ✅ No bearer_token here - uses main one
        }
    }
    print(json.dumps(new_request, indent=2))
    
    print("\n🎯 Benefits:")
    print("  - Single authentication token")
    print("  - No duplicate fields")
    print("  - Cleaner API design")
    print("  - Less confusing for users")
    print("  - Easier to integrate")
    print()

def main():
    """Run all tests for single bearer token implementation."""
    print("🔑 Single Bearer Token Validation API Test Suite")
    print("=" * 60)
    
    # Show comparison first
    compare_old_vs_new_request()
    
    # Test 1: Traditional validation (no change)
    try:
        test_traditional_validation()
    except Exception as e:
        print(f"❌ Traditional validation test failed: {e}\n")
    
    # Test 2: Upload & Validate with direct client code
    try:
        test_upload_and_validate_with_direct_client()
    except Exception as e:
        print(f"❌ Upload & validate (direct client) test failed: {e}\n")
    
    # Test 3: Upload & Validate with order details API
    try:
        test_upload_and_validate_with_order_details()
    except Exception as e:
        print(f"❌ Upload & validate (order details) test failed: {e}\n")
    
    # Test 4: Async processing
    try:
        test_async_upload_and_validate()
    except Exception as e:
        print(f"❌ Async upload & validate test failed: {e}\n")
    
    print("🎉 Single Bearer Token Test Suite Completed!")
    print("\n✅ Key Benefits Demonstrated:")
    print("  1. Single bearer_token for all operations")
    print("  2. Cleaner request structure")
    print("  3. No duplicate token fields")
    print("  4. Backward compatibility maintained")
    print("  5. All processing modes work (sync/async)")

if __name__ == "__main__":
    main()
