.venv/
.env

**/__pycache__/
**/*.py[cod]

icp-ai/

# Virtual Environment
.venv/
venv/
env/

# Environment Variables
.env
.env.local
.env.*.local

# Python Cache
**/__pycache__/
**/*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
icp-ai/
data/uploads/*
!data/uploads/.gitkeep
data/vector_db/*
!data/vector_db/.gitkeep
data/processed/*
!data/processed/.gitkeep

# Note: ChromaDB now uses external ../VectorDB directory
chroma/*
# Logs
*.log
logs/