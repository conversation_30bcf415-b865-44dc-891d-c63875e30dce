#!/usr/bin/env python3
"""
Temporarily disable LLM cache to test currency conversion changes.
Run this before testing the API to ensure fresh responses.
"""

import os
from pathlib import Path

def disable_cache_temporarily():
    """Modify config to disable LLM cache temporarily."""
    config_path = Path("app/core/config.py")
    
    if not config_path.exists():
        print("❌ Config file not found")
        return
    
    # Read current config
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Check if already disabled
    if "DISABLE_LLM_CACHE: bool = True" in content:
        print("✅ LLM cache is already disabled")
        return
    
    # Replace the setting
    if "DISABLE_LLM_CACHE: bool = False" in content:
        new_content = content.replace(
            "DISABLE_LLM_CACHE: bool = False",
            "DISABLE_LLM_CACHE: bool = True"
        )
        
        # Write back
        with open(config_path, 'w') as f:
            f.write(new_content)
        
        print("✅ Disabled LLM cache for testing")
        print("🔄 Please restart your API server to apply changes")
        print()
        print("📝 To re-enable cache later, change:")
        print("   DISABLE_LLM_CACHE: bool = True")
        print("   back to:")
        print("   DISABLE_LLM_CACHE: bool = False")
    else:
        print("❌ Could not find DISABLE_LLM_CACHE setting in config")

def enable_cache():
    """Re-enable LLM cache."""
    config_path = Path("app/core/config.py")
    
    if not config_path.exists():
        print("❌ Config file not found")
        return
    
    # Read current config
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Replace the setting
    if "DISABLE_LLM_CACHE: bool = True" in content:
        new_content = content.replace(
            "DISABLE_LLM_CACHE: bool = True",
            "DISABLE_LLM_CACHE: bool = False"
        )
        
        # Write back
        with open(config_path, 'w') as f:
            f.write(new_content)
        
        print("✅ Re-enabled LLM cache")
        print("🔄 Please restart your API server to apply changes")
    else:
        print("✅ LLM cache is already enabled")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "enable":
        enable_cache()
    else:
        disable_cache_temporarily()
        print()
        print("🧪 Testing Instructions:")
        print("1. Restart your API server")
        print("2. Test the currency conversion with your API call")
        print("3. Run 'python disable_cache_for_testing.py enable' to re-enable cache")
