#!/usr/bin/env python3
"""
Test script to demonstrate holistic validation vs individual validation.
"""

import requests
import json
import time

def test_holistic_vs_individual_validation():
    """Compare holistic validation with individual validation."""
    
    print("Testing Holistic vs Individual Validation")
    print("=" * 60)
    
    base_url = "http://localhost:8000/api/v1"
    
    # Test payload
    test_payload = {
        "report_id": "test-report-123",
        "bearer_token": "your-api-token",
        "direct_client_code": "TURKEXIM",
        "validation_options": {
            "focus_prompt": "Perform comprehensive analysis considering all sections and their relationships"
        }
    }
    
    print("Test Payload:")
    print(json.dumps(test_payload, indent=2))
    print("\n" + "=" * 60 + "\n")
    
    # Test 1: Holistic Validation (if enabled)
    print("Test 1: Holistic Validation")
    print("-" * 30)
    
    try:
        start_time = time.time()
        response = requests.post(f"{base_url}/validate", json=test_payload, timeout=120)
        end_time = time.time()
        
        print(f"Status Code: {response.status_code}")
        print(f"Processing Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("Holistic validation successful!")
            
            # Analyze results
            results = result.get('results', [])
            print(f"Total questions processed: {len(results)}")
            
            # Check for holistic features
            cross_ref_count = sum(1 for r in results if r.get('cross_references'))
            reasoning_count = sum(1 for r in results if r.get('reasoning'))
            
            print(f"Results with cross-references: {cross_ref_count}")
            print(f"Results with detailed reasoning: {reasoning_count}")
            
            # Show sample results
            print("\nSample Results:")
            for i, res in enumerate(results[:3]):
                print(f"  {i+1}. Question: {res.get('question', '')[:60]}...")
                print(f"     Summary: {res.get('summary', '')}")
                print(f"     Status: {res.get('status', '')}")
                print(f"     Confidence: {res.get('confidence_score', 0):.2f}")
                if res.get('cross_references'):
                    print(f"     Cross-refs: {res.get('cross_references', [])}")
                print()
            
            # Check for holistic indicators
            holistic_indicators = {
                "rag_enabled": result.get('rag_enabled', False),
                "processing_approach": "holistic" if cross_ref_count > 0 else "individual",
                "context_awareness": "high" if reasoning_count > len(results) * 0.8 else "standard"
            }
            
            print("Holistic Validation Analysis:")
            for key, value in holistic_indicators.items():
                print(f"  {key}: {value}")
                
        else:
            print(f"Holistic validation failed: {response.status_code}")
            print(f"Error: {response.text}")

    except Exception as e:
        print(f"Holistic validation error: {e}")

    print("\n" + "=" * 60 + "\n")

    # Test 2: Check Configuration
    print("Test 2: Check Holistic Validation Configuration")
    print("-" * 30)
    
    try:
        # Check if holistic validation is enabled
        config_response = requests.get(f"{base_url}/rag/status", timeout=10)
        
        if config_response.status_code == 200:
            config = config_response.json()
            print("Configuration Status:")
            print(f"  RAG enabled: {config.get('rag_enabled', False)}")
            print(f"  Examples in knowledge base: {config.get('examples_collection', {}).get('count', 0)}")
            print(f"  Patterns available: {config.get('patterns_collection', {}).get('count', 0)}")
            print(f"  Embeddings enabled: {config.get('embeddings_enabled', False)}")
        else:
            print(f"Configuration check failed: {config_response.status_code}")

    except Exception as e:
        print(f"Configuration check error: {e}")

    print("\n" + "=" * 60 + "\n")

    # Test 3: Compare Response Quality
    print("Test 3: Response Quality Analysis")
    print("-" * 30)
    
    try:
        # Run validation again to analyze quality
        response = requests.post(f"{base_url}/validate", json=test_payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            results = result.get('results', [])
            
            # Quality metrics
            quality_metrics = {
                "total_questions": len(results),
                "avg_confidence": sum(r.get('confidence_score', 0) for r in results) / len(results) if results else 0,
                "high_confidence_count": sum(1 for r in results if r.get('confidence_score', 0) > 0.8),
                "error_count": sum(1 for r in results if r.get('status') == 'error'),
                "approved_count": sum(1 for r in results if r.get('status') == 'approved'),
                "rejected_count": sum(1 for r in results if r.get('status') == 'rejected'),
                "manual_review_count": sum(1 for r in results if r.get('status') == 'manual_intervention_needed')
            }
            
            print("Quality Metrics:")
            for metric, value in quality_metrics.items():
                if isinstance(value, float):
                    print(f"  {metric}: {value:.3f}")
                else:
                    print(f"  {metric}: {value}")
            
            # Check for comprehensive analysis indicators
            comprehensive_indicators = []
            
            if quality_metrics["avg_confidence"] > 0.8:
                comprehensive_indicators.append("High average confidence")

            if quality_metrics["error_count"] == 0:
                comprehensive_indicators.append("No processing errors")

            if cross_ref_count > 0:
                comprehensive_indicators.append("Cross-referencing enabled")

            if reasoning_count > len(results) * 0.7:
                comprehensive_indicators.append("Detailed reasoning provided")
            
            print("\nComprehensive Analysis Indicators:")
            for indicator in comprehensive_indicators:
                print(f"  {indicator}")
            
            if not comprehensive_indicators:
                print("  No comprehensive analysis indicators found")
                print("  Consider enabling holistic validation and RAG")
                
        else:
            print(f"Quality analysis failed: {response.status_code}")

    except Exception as e:
        print(f"Quality analysis error: {e}")

def test_holistic_features():
    """Test specific holistic validation features."""

    print("\n" + "Testing Holistic Validation Features")
    print("=" * 60)
    
    # Test with focus on cross-references
    test_payload = {
        "report_id": "test-report-123",
        "validation_options": {
            "focus_prompt": "Identify relationships between company information, payment details, and compliance status. Look for inconsistencies across different sections."
        }
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/validate", json=test_payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            results = result.get('results', [])
            
            print("Cross-Reference Analysis:")
            
            # Find results with cross-references
            cross_ref_results = [r for r in results if r.get('cross_references')]
            
            if cross_ref_results:
                print(f"  Found {len(cross_ref_results)} results with cross-references")
                
                for i, res in enumerate(cross_ref_results[:3], 1):
                    print(f"\n  Example {i}:")
                    print(f"    Question: {res.get('question', '')[:60]}...")
                    print(f"    Summary: {res.get('summary', '')}")
                    print(f"    Cross-refs: {res.get('cross_references', [])}")
                    print(f"    Reasoning: {res.get('reasoning', '')[:100]}...")
            else:
                print("  No cross-references found")
                print("  Holistic validation may not be fully enabled")

            print("\nReasoning Quality Analysis:")
            
            # Analyze reasoning quality
            detailed_reasoning = [r for r in results if r.get('reasoning') and len(r.get('reasoning', '')) > 50]
            
            if detailed_reasoning:
                print(f"  Found {len(detailed_reasoning)} results with detailed reasoning")
                
                avg_reasoning_length = sum(len(r.get('reasoning', '')) for r in detailed_reasoning) / len(detailed_reasoning)
                print(f"  Average reasoning length: {avg_reasoning_length:.0f} characters")
            else:
                print("  Limited detailed reasoning found")

        else:
            print(f"Holistic features test failed: {response.status_code}")

    except Exception as e:
        print(f"Holistic features test error: {e}")

if __name__ == "__main__":
    print("Holistic Validation Test Suite")
    print("=" * 60)
    print("This test compares holistic vs individual validation approaches")
    print("Make sure the server is running and RAG is initialized")
    print("=" * 60)
    
    # Run main tests
    test_holistic_vs_individual_validation()
    
    # Run feature-specific tests
    test_holistic_features()
    
    print("\nTest Suite Complete!")
    print("\nKey Indicators of Holistic Validation:")
    print("Cross-references between validation rules")
    print("Detailed reasoning considering full context")
    print("Higher confidence scores")
    print("Consistent analysis across related sections")
    print("RAG-enhanced responses with examples")
