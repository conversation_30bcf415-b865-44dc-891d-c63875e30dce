#!/usr/bin/env python3
"""
Quick test to verify RAG speed optimization is working
"""

import asyncio
import time
import aiohttp
import json

# Test configuration
API_BASE_URL = "http://localhost:8000"
BEARER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTczNzgwNzE5Nn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

# Test payload
TEST_PAYLOAD = {
    "report_id": "test_report_001",
    "enable_client_filtering": True,
    "order_details_params": {
        "bearer_token": BEARER_TOKEN,
        "order_id": "12345"
    },
    "validation_options": {
        "include_low_confidence": True,
        "min_confidence_threshold": 0.3
    }
}

async def test_single_request():
    """Test a single validation request and measure time."""
    
    print("🧪 Testing RAG Speed Configuration")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        print("📡 Sending validation request...")
        start_time = time.time()
        
        try:
            async with session.post(
                f"{API_BASE_URL}/validate",
                json=TEST_PAYLOAD,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    print(f"✅ Request completed successfully!")
                    print(f"⏱️  Total time: {duration:.2f} seconds")
                    print(f"📊 Questions processed: {result.get('processed_questions', 0)}")
                    print(f"📊 Questions skipped: {result.get('skipped_questions', 0)}")
                    
                    # Check if we can see the RAG setting in logs
                    print("\n🔍 Check your server logs for:")
                    print("   - '🔧 SKIP_RAG_FOR_SPEED setting: True/False'")
                    print("   - '⚡ Skipping RAG retrieval for speed optimization'")
                    
                    return duration
                    
                else:
                    print(f"❌ API Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ Exception: {e}")
            return None

async def test_config_verification():
    """Test to verify current configuration."""
    
    print("\n🔧 Configuration Verification")
    print("-" * 30)
    
    # Try to get current config (if there's an endpoint for it)
    async with aiohttp.ClientSession() as session:
        try:
            # Check if there's a health or config endpoint
            async with session.get(f"{API_BASE_URL}/") as response:
                if response.status == 200:
                    print("✅ API server is running")
                else:
                    print(f"⚠️  API server responded with status: {response.status}")
        except Exception as e:
            print(f"❌ Cannot connect to API server: {e}")
            print("Make sure the server is running on http://localhost:8000")

if __name__ == "__main__":
    print("🚀 RAG Speed Test")
    print("Make sure you've restarted the API server after changing config!")
    print()
    
    try:
        # Verify API is accessible
        asyncio.run(test_config_verification())
        
        # Run the test
        duration = asyncio.run(test_single_request())
        
        if duration:
            print(f"\n📈 Performance Analysis:")
            if duration < 20:
                print("🟢 Good performance - optimizations appear to be working")
            elif duration < 40:
                print("🟡 Moderate performance - some optimizations may be active")
            else:
                print("🔴 Slow performance - optimizations may not be active")
                
            print(f"\n💡 Tips:")
            print("1. Check server logs for RAG skip messages")
            print("2. Try changing SKIP_RAG_FOR_SPEED and restart server")
            print("3. Compare times with different settings")
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
