#!/usr/bin/env python3
"""
Test XPF (CFP Franc) currency conversion for company-credit validation.
This test verifies that 1,000,000 XPF is correctly converted to GBP and classified as small credit.
"""

import asyncio
import json
import tempfile
import os
from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.models.schemas import Question

# Test XML data with XPF currency and exchange rates
TEST_XML_XPF = """<?xml version="1.0" encoding="UTF-8"?>
<DarwinReport>
    <CompanySection>
        <Company>
            <CompanyName>PetiteCorp</CompanyName>
            <NumberOfEmployees>25</NumberOfEmployees>
            <AnnualTurnover currency="XPF">50000000</AnnualTurnover>
            <CompanyType>Small Business</CompanyType>
        </Company>
    </CompanySection>
    
    <PaymentsSection>
        <CreditOpinion>
            <MaxCredit currency="XPF">1000000</MaxCredit>
            <CreditLimit currency="XPF">1000000</CreditLimit>
            <CreditRating>Good</CreditRating>
        </CreditOpinion>
    </PaymentsSection>
    
    <ExchangeRates>
        <Rate>
            <FromCurrency>XPF</FromCurrency>
            <ToCurrency>GBP</ToCurrency>
            <Rate>0.0075</Rate>
            <Date>2024-07-29</Date>
        </Rate>
        <Rate>
            <FromCurrency>XPF</FromCurrency>
            <ToCurrency>EUR</ToCurrency>
            <Rate>0.0084</Rate>
            <Date>2024-07-29</Date>
        </Rate>
    </ExchangeRates>
    
    <FinancialSection>
        <TotalIncome currency="XPF">50000000</TotalIncome>
        <Currency>XPF</Currency>
    </FinancialSection>
</DarwinReport>"""

# Test questions for XPF currency validation
TEST_QUESTIONS_XPF = [
    {
        "id": "xpf-small-company-large-credit",
        "question": "Mark as an issue if a small company is associated with a large credit amount.",
        "expected_outcome": "approved",  # Should be approved because 1M XPF = £7,500 (small credit)
        "description": "Test XPF currency conversion - 1,000,000 XPF should be classified as small credit"
    },
    {
        "id": "xpf-large-company-small-credit", 
        "question": "Mark as an issue if a large company is associated with a small credit amount.",
        "expected_outcome": "approved",  # Should be approved because company is small, not large
        "description": "Test that small company doesn't trigger large company rule"
    },
    {
        "id": "xpf-currency-conversion-verification",
        "question": "Verify that credit amounts are properly converted to GBP for company size comparison using available exchange rates.",
        "expected_outcome": "approved",
        "description": "Test that XPF to GBP conversion is performed correctly"
    }
]

async def test_xpf_currency_conversion():
    """Test XPF currency conversion in company-credit validation."""
    print("🧪 Testing XPF Currency Conversion for Company-Credit Validation")
    print("=" * 80)

    # Create a temporary XML file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as temp_file:
        temp_file.write(TEST_XML_XPF)
        temp_xml_path = temp_file.name

    try:
        # Initialize services
        file_processor = FileProcessor()
        validation_service = ValidationService()

        # Process the XML file to get report data
        report_id = "test-xpf-conversion"
        report_data = await file_processor.process_xml_report(temp_xml_path, report_id)

        print(f"📋 Test Data:")
        print(f"   Company: PetiteCorp (25 employees, small company)")
        print(f"   Credit: 1,000,000 XPF")
        print(f"   Exchange Rate: 1 XPF = 0.0075 GBP")
        print(f"   Expected GBP: 1,000,000 × 0.0075 = £7,500 GBP (Small Credit)")
        print()

        # Test each validation question
        for i, question_data in enumerate(TEST_QUESTIONS_XPF, 1):
            print(f"🔍 Test {i}: {question_data['description']}")
            print(f"   Question: {question_data['question']}")
            print(f"   Expected: {question_data['expected_outcome']}")

            try:
                # Create Question object
                question = Question(
                    id=question_data['id'],
                    question=question_data['question'],
                    darwin_reference_sections="PaymentsSection/CreditOpinion, PaymentsSection/MaxCredit, CompanySection/Company",
                    expected_outcome=question_data['expected_outcome'],
                    client_specific_type="All"
                )

                # Run validation using the internal method
                result = await validation_service._validate_single_question(
                    question=question,
                    report_data=report_data,
                    question_number=i
                )

                print(f"   Result: {result.status}")
                print(f"   Summary: {result.summary}")
                print(f"   Reasoning: {result.reasoning}")
                print(f"   Confidence: {result.confidence_score}")

                # Check if currency conversion is mentioned
                reasoning = result.reasoning.lower() if result.reasoning else ""
                summary = result.summary.lower() if result.summary else ""

                conversion_mentioned = any(term in reasoning + summary for term in [
                    'xpf', 'gbp', 'convert', 'exchange', '7,500', '7500', 'small credit', '7.5'
                ])

                if conversion_mentioned:
                    print(f"   ✅ Currency conversion detected in response")
                else:
                    print(f"   ❌ Currency conversion NOT detected in response")

                # Check expected outcome
                actual_status = result.status
                expected_status = question_data['expected_outcome']

                if actual_status == expected_status:
                    print(f"   ✅ Status matches expected: {actual_status}")
                else:
                    print(f"   ❌ Status mismatch - Expected: {expected_status}, Got: {actual_status}")

            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                import traceback
                traceback.print_exc()

            print()

    finally:
        # Clean up temporary file
        if os.path.exists(temp_xml_path):
            os.unlink(temp_xml_path)
    
    print("📊 Key Validation Points:")
    print("1. 1,000,000 XPF should be converted to £7,500 GBP")
    print("2. £7,500 GBP should be classified as 'Small Credit' (<£50,000)")
    print("3. Small company + Small credit should be 'approved' (no mismatch)")
    print("4. Response should show the currency conversion calculation")
    print()
    
    print("💡 Expected Behavior:")
    print("- Question 1: APPROVED (small company + small credit = no issue)")
    print("- Question 2: APPROVED (company is small, rule doesn't apply)")  
    print("- Question 3: APPROVED (conversion performed correctly)")
    print()
    
    print("🔧 If tests fail, check:")
    print("1. Exchange rate extraction from XML")
    print("2. Currency conversion calculation (1M XPF × 0.0075 = £7,500)")
    print("3. Credit size classification (£7,500 = Small Credit)")
    print("4. Company size classification (25 employees = Small Company)")

if __name__ == "__main__":
    asyncio.run(test_xpf_currency_conversion())
