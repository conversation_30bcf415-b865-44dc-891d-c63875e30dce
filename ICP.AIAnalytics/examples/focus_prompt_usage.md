# Focus Prompt Feature Usage Examples

The `focus_prompt` field in the validation API allows frontend users to regenerate reports with specific focus areas, new questions, or emphasis on particular sections.

## Basic Usage

### Example 1: Focus on Specific Sections

```json
{
    "report_id": "RPT-2024-001",
    "bearer_token": "your-api-token",
    "direct_client_code": "TURKEXIM",
    "validation_options": {
        "focus_prompt": "Focus on payment-related sections and analyze currency compliance in detail"
    }
}
```

### Example 2: New Question or Angle

```json
{
    "report_id": "RPT-2024-001",
    "bearer_token": "your-api-token",
    "direct_client_code": "TURKEXIM",
    "validation_options": {
        "focus_prompt": "Are there any potential red flags in the company's financial structure that might indicate risk?"
    }
}
```

### Example 3: Section Emphasis

```json
{
    "report_id": "RPT-2024-001",
    "bearer_token": "your-api-token",
    "direct_client_code": "TURKEXIM",
    "validation_options": {
        "focus_prompt": "Examine company name and registration details more thoroughly, check for any discrepancies between requested and actual information"
    }
}
```

### Example 4: Compliance-Specific Query

```json
{
    "report_id": "RPT-2024-001",
    "bearer_token": "your-api-token",
    "direct_client_code": "TURKEXIM",
    "validation_options": {
        "focus_prompt": "Review all regulatory compliance aspects, particularly focusing on anti-money laundering requirements"
    }
}
```

## Use Cases

### 1. **Follow-up Questions**
When users want to ask additional questions about the report:
- "What are the key risk indicators in this report?"
- "Are there any unusual patterns in the financial data?"
- "How does this company compare to industry standards?"

### 2. **Section Deep-Dive**
When users want to focus on specific sections:
- "Focus on the corporate structure section"
- "Analyze the payment and banking details more thoroughly"
- "Examine the beneficial ownership information in detail"

### 3. **Compliance Focus**
When users need specific compliance checks:
- "Check for sanctions compliance indicators"
- "Review KYC documentation completeness"
- "Analyze for potential PEP (Politically Exposed Person) connections"

### 4. **Risk Assessment**
When users want risk-focused analysis:
- "Identify potential red flags or warning signs"
- "Assess the overall risk profile of this entity"
- "Look for inconsistencies that might indicate fraud"

## How It Works

1. **Frontend Input**: Users provide a focus prompt through the UI
2. **API Processing**: The prompt is passed to the validation service
3. **Enhanced Analysis**: The LLM incorporates the focus prompt into its analysis
4. **Targeted Results**: Validation results are generated with the specified focus

## Integration with Existing Features

The focus prompt works alongside existing features:
- **Darwin Reference Sections**: Focus prompts complement Darwin targeting
- **Client Filtering**: Works with client-specific validation rules
- **Validation Options**: Compatible with all existing validation options
- **Async Processing**: Supports both synchronous and asynchronous processing

## Best Practices

### Effective Focus Prompts
- Be specific about what you want to analyze
- Use clear, actionable language
- Focus on business-relevant aspects
- Avoid overly technical jargon

### Examples of Good Focus Prompts
✅ "Focus on payment methods and currency compliance"
✅ "Examine corporate structure for potential ownership risks"
✅ "Check for consistency between company registration and operational details"

### Examples to Avoid
❌ "Make it better"
❌ "Check everything"
❌ "Look at the XML structure"

## Response Format

The API response remains the same, but the validation results will reflect the focus:

```json
{
    "validation_id": "750e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "total_questions": 41,
    "processed_questions": 41,
    "results": [
        {
            "question_id": "q1",
            "question": "What is the primary client information?",
            "summary": "Primary client ABC Corp shows consistent payment methods across USD and EUR currencies, compliant with focus requirements",
            "confidence_score": 0.95,
            "status": "approved"
        }
    ]
}
```

Note how the summary reflects the focus on "payment methods and currency compliance" from the focus prompt.
