#!/usr/bin/env python3
"""
Debug currency conversion issue - test with minimal setup
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from services.validation_service import ValidationService
from models.schemas import Question

# Test XML with XPF currency and exchange rate
TEST_XML = """<?xml version="1.0" encoding="UTF-8"?>
<Report>
    <Header>
        <CompanyName>Small Test Company</CompanyName>
        <Employees>25</Employees>
        <AnnualTurnover currency="EUR">500000</AnnualTurnover>
    </Header>
    <Credit>
        <MaxCreditAmount currency="XPF">1000000</MaxCreditAmount>
        <CreditLimit>1000000 XPF</CreditLimit>
    </Credit>
    <ExchangeRates>
        <Rate>
            <FromCurrency>XPF</FromCurrency>
            <ToCurrency>GBP</ToCurrency>
            <Rate>0.0075</Rate>
        </Rate>
        <Rate>
            <FromCurrency>EUR</FromCurrency>
            <ToCurrency>GBP</ToCurrency>
            <Rate>0.86</Rate>
        </Rate>
    </ExchangeRates>
</Report>"""

# Test question for company-credit validation
TEST_QUESTION = Question(
    id="test-currency-1",
    question="Mark as an issue if a small company is associated with a large credit amount.",
    expected_outcome="approved",
    darwin_reference_sections="(Credit) Max Credit Amount, (Header) Company Size"
)

async def test_currency_conversion():
    """Test currency conversion with debug output."""
    print("=== CURRENCY CONVERSION DEBUG TEST ===")
    print()
    
    # Initialize validation service
    validation_service = ValidationService()
    
    # Clear any existing cache
    validation_service.clear_llm_cache()
    print("✓ Cleared LLM cache")
    
    # Test the prompt generation
    print("\n=== TESTING PROMPT GENERATION ===")
    prompt = validation_service._build_enhanced_validation_prompt(TEST_QUESTION, TEST_XML, 1)
    
    # Show the full prompt to debug
    print("FULL PROMPT:")
    print("=" * 80)
    print(prompt)
    print("=" * 80)
    
    # Test LLM response generation directly
    print("\n=== TESTING LLM RESPONSE GENERATION ===")
    print("Test XML contains:")
    print("- Company: Small Test Company (25 employees)")
    print("- Credit: 1,000,000 XPF")
    print("- Exchange Rate: XPF to GBP = 0.0075")
    print("- Expected: 1,000,000 XPF × 0.0075 = £7,500 GBP (Small Credit)")
    print("- Expected Result: Small company + Small credit = APPROVED")
    print()

    try:
        # Test LLM response generation directly
        if validation_service.llm:
            print("✓ LLM is available")

            # Generate LLM response directly
            llm_response = await validation_service._generate_enhanced_llm_response(
                TEST_QUESTION,
                TEST_XML,
                1
            )

            print("=== LLM RESPONSE ===")
            print(llm_response)
            print("=" * 50)

            # Check if currency conversion was performed
            if "XPF" in llm_response and "GBP" in llm_response:
                print("✓ Currency conversion mentioned in response")
            else:
                print("❌ No currency conversion found in response")

            if "7,500" in llm_response or "7500" in llm_response:
                print("✓ Correct GBP conversion amount found")
            else:
                print("❌ Correct GBP conversion amount NOT found")

            if "approved" in llm_response.lower():
                print("✓ Correct status (approved)")
            else:
                print(f"❌ Incorrect status in response")

        else:
            print("❌ No LLM available - this is likely the issue!")
            print("Check OPENAI_MODEL configuration in config.py")

    except Exception as e:
        print(f"❌ LLM response generation failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Set environment for testing
    os.environ["DISABLE_LLM_CACHE"] = "true"
    os.environ["REDUCE_LLM_CONTEXT"] = "false"
    
    asyncio.run(test_currency_conversion())
