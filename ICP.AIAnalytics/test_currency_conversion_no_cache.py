#!/usr/bin/env python3
"""
Test currency conversion with cache disabled to ensure prompt changes take effect.
"""

import os
import sys

# Set environment variable to disable LLM cache
os.environ['DISABLE_LLM_CACHE'] = 'true'

import asyncio
import json
from app.services.validation_service import ValidationService

async def test_currency_conversion_no_cache():
    """Test currency conversion with cache disabled."""
    print("🧪 Testing Currency Conversion with Cache Disabled")
    print("=" * 60)
    
    # Initialize validation service
    validation_service = ValidationService()
    
    # Clear any existing cache
    validation_service.clear_llm_cache()
    print("🗑️ Cleared LLM cache")
    
    # Test data - simple XML with XPF currency
    test_xml = """<?xml version="1.0" encoding="UTF-8"?>
<DarwinReport>
    <CompanySection>
        <Company>
            <CompanyName>TestCorp</CompanyName>
            <NumberOfEmployees>25</NumberOfEmployees>
            <AnnualTurnover currency="XPF">50000000</AnnualTurnover>
        </Company>
    </CompanySection>
    
    <PaymentsSection>
        <CreditOpinion>
            <MaxCredit currency="XPF">1000000</MaxCredit>
            <CreditRating>Good</CreditRating>
        </CreditOpinion>
    </PaymentsSection>
    
    <ExchangeRates>
        <Rate>
            <FromCurrency>XPF</FromCurrency>
            <ToCurrency>GBP</ToCurrency>
            <Rate>0.0075</Rate>
        </Rate>
    </ExchangeRates>
</DarwinReport>"""
    
    # Test question
    test_question = "Mark as an issue if a small company is associated with a large credit amount."
    
    print(f"📋 Test Setup:")
    print(f"   Company: TestCorp (25 employees = Small Company)")
    print(f"   Credit: 1,000,000 XPF")
    print(f"   Exchange Rate: 1 XPF = 0.0075 GBP")
    print(f"   Expected: 1,000,000 XPF × 0.0075 = £7,500 GBP (Small Credit)")
    print(f"   Expected Result: APPROVED (Small Company + Small Credit = No Issue)")
    print()
    
    try:
        # Create a simple report data structure
        report_data = {
            'xml_data': {
                'DarwinReport': {
                    'CompanySection': {
                        'Company': {
                            'CompanyName': 'TestCorp',
                            'NumberOfEmployees': '25',
                            'AnnualTurnover': {'@currency': 'XPF', '#text': '50000000'}
                        }
                    },
                    'PaymentsSection': {
                        'CreditOpinion': {
                            'MaxCredit': {'@currency': 'XPF', '#text': '1000000'},
                            'CreditRating': 'Good'
                        }
                    },
                    'ExchangeRates': {
                        'Rate': {
                            'FromCurrency': 'XPF',
                            'ToCurrency': 'GBP',
                            'Rate': '0.0075'
                        }
                    }
                }
            },
            'xml_content': test_xml
        }
        
        # Create Question object
        from app.models.schemas import Question
        question = Question(
            id="test-currency-conversion",
            question=test_question,
            darwin_reference_sections="PaymentsSection/CreditOpinion, CompanySection/Company",
            expected_outcome="approved",
            client_specific_type="All"
        )
        
        print("🔍 Running validation with enhanced currency conversion prompt...")
        
        # Run validation
        result = await validation_service._validate_single_question(
            question=question,
            report_data=report_data,
            question_number=1
        )
        
        print(f"📊 Results:")
        print(f"   Status: {result.status}")
        print(f"   Summary: {result.summary}")
        print(f"   Confidence: {result.confidence_score}")
        print()
        print(f"🔍 Reasoning:")
        print(f"   {result.reasoning}")
        print()
        
        # Check for currency conversion indicators
        reasoning_text = result.reasoning.lower() if result.reasoning else ""
        summary_text = result.summary.lower() if result.summary else ""
        combined_text = reasoning_text + " " + summary_text
        
        conversion_indicators = [
            'xpf', 'gbp', '7,500', '7500', 'convert', 'exchange', 
            'rate', '0.0075', 'small credit', 'currency'
        ]
        
        found_indicators = [indicator for indicator in conversion_indicators 
                          if indicator in combined_text]
        
        print(f"✅ Currency Conversion Analysis:")
        if found_indicators:
            print(f"   Found indicators: {', '.join(found_indicators)}")
            print(f"   ✅ Currency conversion appears to be working")
        else:
            print(f"   ❌ No currency conversion indicators found")
            print(f"   ❌ Currency conversion may not be working")
        
        print()
        print(f"🎯 Expected vs Actual:")
        print(f"   Expected Status: approved")
        print(f"   Actual Status: {result.status}")
        
        if result.status == "approved":
            print(f"   ✅ Status matches expected result")
        else:
            print(f"   ❌ Status does not match - currency conversion likely failed")
        
    except Exception as e:
        print(f"❌ Error during validation: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_currency_conversion_no_cache())
