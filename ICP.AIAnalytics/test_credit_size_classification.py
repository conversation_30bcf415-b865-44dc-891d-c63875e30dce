#!/usr/bin/env python3
"""
Test script to verify credit size classification functionality in validation prompts.
This test ensures that the LLM correctly classifies credit amounts based on GBP thresholds.
"""

import asyncio
import json
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.validation_service import ValidationService
from app.models.schemas import Question

# Test XML content with different credit amounts in various currencies
TEST_XML_CONTENT = """<?xml version="1.0" encoding="UTF-8"?>
<Report>
    <HeaderSection>
        <CompanyName>Test Company Ltd</CompanyName>
        <Requested>Test Company Ltd</Requested>
    </HeaderSection>
    <CreditInformation>
        <MaxCredit>
            <Amount>75000</Amount>
            <Currency>GBP</Currency>
        </MaxCredit>
        <CreditLimit>
            <Amount>100000</Amount>
            <Currency>USD</Currency>
        </CreditLimit>
        <ExchangeRates>
            <Rate>
                <FromCurrency>USD</FromCurrency>
                <ToCurrency>GBP</ToCurrency>
                <Rate>0.79</Rate>
            </Rate>
            <Rate>
                <FromCurrency>EUR</FromCurrency>
                <ToCurrency>GBP</ToCurrency>
                <Rate>0.86</Rate>
            </Rate>
        </ExchangeRates>
    </CreditInformation>
    <FinancialInformation>
        <TotalIncome>
            <Amount>500000</Amount>
            <Currency>EUR</Currency>
        </TotalIncome>
        <GrossProfit>
            <Amount>25000</Amount>
            <Currency>GBP</Currency>
        </GrossProfit>
    </FinancialInformation>
</Report>"""

# Test questions for different credit size scenarios
TEST_QUESTIONS = [
    {
        "question": "Classify the Max Credit amount as Small, Medium, or Large credit based on GBP thresholds",
        "expected_classification": "Medium",  # 75,000 GBP
        "description": "Direct GBP amount - Medium credit"
    },
    {
        "question": "Determine if the Credit Limit qualifies as Large credit when converted to GBP",
        "expected_classification": "Medium",  # 100,000 USD * 0.79 = 79,000 GBP
        "description": "USD to GBP conversion - Medium credit"
    },
    {
        "question": "Classify the Total Income as Small, Medium, or Large credit after currency conversion",
        "expected_classification": "Large",  # 500,000 EUR * 0.86 = 430,000 GBP
        "description": "EUR to GBP conversion - Large credit"
    },
    {
        "question": "Determine the credit size category for Gross Profit amount",
        "expected_classification": "Small",  # 25,000 GBP
        "description": "Direct GBP amount - Small credit"
    }
]

async def test_credit_size_classification():
    """Test the credit size classification functionality."""
    print("🧪 Testing Credit Size Classification Functionality")
    print("=" * 60)
    
    try:
        # Initialize validation service
        validation_service = ValidationService()
        
        # Test each question
        for i, test_case in enumerate(TEST_QUESTIONS, 1):
            print(f"\n📋 Test Case {i}: {test_case['description']}")
            print(f"Question: {test_case['question']}")
            print(f"Expected Classification: {test_case['expected_classification']}")
            
            # Create Question object
            question = Question(
                id=f"test-question-{i}",
                question=test_case['question'],
                darwin_reference_sections="(Payments) Max Credit Currency, (Financial) Credit Information",
                expected_outcome="approved",
                client_specific_type="All"
            )
            
            try:
                # Generate validation response
                if validation_service.llm:
                    response = await validation_service._generate_enhanced_llm_response(
                        question=question,
                        xml_content=TEST_XML_CONTENT,
                        question_number=i
                    )
                    
                    # Parse the response
                    parsed_response = validation_service._parse_llm_response(response)
                    
                    print(f"✅ LLM Response:")
                    print(f"   Summary: {parsed_response.get('summary', 'N/A')}")
                    print(f"   Status: {parsed_response.get('status', 'N/A')}")
                    print(f"   Confidence: {parsed_response.get('confidence_score', 'N/A')}")
                    print(f"   Reasoning: {parsed_response.get('reasoning', 'N/A')}")
                    
                    # Check if the expected classification is mentioned in the summary
                    summary = parsed_response.get('summary', '').lower()
                    expected_class = test_case['expected_classification'].lower()
                    
                    if expected_class in summary:
                        print(f"✅ PASS: Expected classification '{test_case['expected_classification']}' found in response")
                    else:
                        print(f"❌ FAIL: Expected classification '{test_case['expected_classification']}' not found in response")
                        print(f"   Actual summary: {parsed_response.get('summary', 'N/A')}")
                
                else:
                    print("⚠️  No LLM available - using fallback response")
                    fallback_response = validation_service._generate_fallback_response(question, TEST_XML_CONTENT)
                    parsed_fallback = json.loads(fallback_response)
                    print(f"   Fallback Summary: {parsed_fallback.get('summary', 'N/A')}")
                    
            except Exception as e:
                print(f"❌ Error in test case {i}: {str(e)}")
                
        print("\n" + "=" * 60)
        print("🏁 Credit Size Classification Test Complete")
        
    except Exception as e:
        print(f"❌ Error initializing validation service: {str(e)}")
        return False
    
    return True

async def test_prompt_content():
    """Test that the prompt templates contain the credit size classification rules."""
    print("\n🔍 Testing Prompt Template Content")
    print("=" * 60)
    
    try:
        validation_service = ValidationService()
        
        # Create a test question
        question = Question(
            id="test-prompt-question",
            question="Test credit classification prompt content",
            darwin_reference_sections="(Payments) Max Credit",
            expected_outcome="approved",
            client_specific_type="All"
        )
        
        # Build the enhanced validation prompt
        prompt = validation_service._build_enhanced_validation_prompt(
            question=question,
            xml_content="<test>content</test>",
            question_number=1
        )
        
        # Check for credit size classification content
        required_content = [
            "CREDIT SIZE CLASSIFICATION RULES",
            "Small Credit: Less than GBP 50,000",
            "Medium Credit: GBP 50,000 - GBP 250,000",
            "Large Credit: Above GBP 250,000",
            "CURRENCY CONVERSION REQUIREMENTS",
            "convert non-GBP amounts to GBP equivalent"
        ]
        
        print("Checking prompt template for required content:")
        all_found = True
        
        for content in required_content:
            if content in prompt:
                print(f"✅ Found: {content}")
            else:
                print(f"❌ Missing: {content}")
                all_found = False
        
        if all_found:
            print("\n✅ All required credit size classification content found in prompt template")
        else:
            print("\n❌ Some required content missing from prompt template")
            
        return all_found
        
    except Exception as e:
        print(f"❌ Error testing prompt content: {str(e)}")
        return False

async def main():
    """Run all credit size classification tests."""
    print("🚀 Starting Credit Size Classification Tests")
    print("=" * 60)
    
    # Test prompt content
    prompt_test_passed = await test_prompt_content()
    
    # Test actual classification
    classification_test_passed = await test_credit_size_classification()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 60)
    print(f"Prompt Content Test: {'✅ PASSED' if prompt_test_passed else '❌ FAILED'}")
    print(f"Classification Test: {'✅ PASSED' if classification_test_passed else '❌ FAILED'}")
    
    if prompt_test_passed and classification_test_passed:
        print("\n🎉 All tests passed! Credit size classification is working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
