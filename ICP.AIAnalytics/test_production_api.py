#!/usr/bin/env python3
"""
Test the production API to verify currency conversion is working
"""
import asyncio
import aiohttp
import json

# Test data with XPF currency
TEST_XML = """<?xml version="1.0" encoding="UTF-8"?>
<Report>
    <Header>
        <CompanyName>Small Test Company</CompanyName>
        <Employees>25</Employees>
        <AnnualTurnover currency="EUR">500000</AnnualTurnover>
    </Header>
    <Credit>
        <MaxCreditAmount currency="XPF">1000000</MaxCreditAmount>
        <CreditLimit>1000000 XPF</CreditLimit>
    </Credit>
    <ExchangeRates>
        <Rate>
            <FromCurrency>XPF</FromCurrency>
            <ToCurrency>GBP</ToCurrency>
            <Rate>0.0075</Rate>
        </Rate>
        <Rate>
            <FromCurrency>EUR</FromCurrency>
            <ToCurrency>GBP</ToCurrency>
            <Rate>0.86</Rate>
        </Rate>
    </ExchangeRates>
</Report>"""

async def test_production_api():
    """Test the production API endpoint"""
    print("=== TESTING PRODUCTION API ===")
    print("Testing currency conversion: 1,000,000 XPF should convert to £7,500 GBP (Small Credit)")
    print("Expected result: Small company + Small credit = APPROVED")
    print()
    
    # First upload the XML file
    upload_url = "http://localhost:8000/upload/xml"
    validation_url = "http://localhost:8000/validate"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Upload XML file
            print("1. Uploading test XML file...")
            form_data = aiohttp.FormData()
            form_data.add_field('file', TEST_XML, filename='test_currency.xml', content_type='application/xml')
            
            async with session.post(upload_url, data=form_data) as response:
                if response.status == 200:
                    upload_result = await response.json()
                    report_id = upload_result.get('report_id')
                    print(f"✓ Upload successful, report_id: {report_id}")
                else:
                    print(f"❌ Upload failed: {response.status}")
                    print(await response.text())
                    return
            
            # Wait a moment for processing
            await asyncio.sleep(2)
            
            # Validate the report
            print("\n2. Running validation...")
            validation_request = {
                "report_id": report_id,
                "validation_options": {},
                "enable_client_filtering": False
            }
            
            async with session.post(validation_url, json=validation_request) as response:
                if response.status == 200:
                    validation_result = await response.json()
                    print("✓ Validation successful")
                    
                    # Find the currency-related question
                    currency_question = None
                    for result in validation_result.get('results', []):
                        if 'small company' in result.get('question', '').lower() and 'large credit' in result.get('question', '').lower():
                            currency_question = result
                            break
                    
                    if currency_question:
                        print("\n=== CURRENCY VALIDATION RESULT ===")
                        print(f"Question: {currency_question.get('question')}")
                        print(f"Summary: {currency_question.get('summary')}")
                        print(f"Status: {currency_question.get('status')}")
                        print(f"Reasoning: {currency_question.get('reasoning')}")
                        print(f"Confidence: {currency_question.get('confidence_score')}")
                        
                        # Check results
                        reasoning = currency_question.get('reasoning', '').lower()
                        status = currency_question.get('status', '')
                        
                        print("\n=== VERIFICATION ===")
                        if 'xpf' in reasoning and 'gbp' in reasoning:
                            print("✓ Currency conversion mentioned")
                        else:
                            print("❌ No currency conversion found")
                            
                        if '7500' in reasoning or '7,500' in reasoning:
                            print("✓ Correct GBP conversion amount (£7,500)")
                        else:
                            print("❌ Incorrect or missing GBP conversion amount")
                            
                        if 'small' in reasoning and 'credit' in reasoning:
                            print("✓ Credit classified as small")
                        elif 'large' in reasoning and 'credit' in reasoning:
                            print("❌ Credit incorrectly classified as large")
                        else:
                            print("? Credit classification unclear")
                            
                        if status == 'approved':
                            print("✓ Correct status (approved)")
                        else:
                            print(f"❌ Incorrect status: {status} (should be approved)")
                            
                        # Overall assessment
                        print("\n=== OVERALL ASSESSMENT ===")
                        if (status == 'approved' and 
                            ('7500' in reasoning or '7,500' in reasoning) and
                            'small' in reasoning and 'credit' in reasoning):
                            print("🎉 CURRENCY CONVERSION IS WORKING CORRECTLY!")
                        else:
                            print("❌ CURRENCY CONVERSION IS STILL NOT WORKING")
                            print("The API is not properly converting XPF to GBP before classification")
                            
                    else:
                        print("❌ Could not find the currency validation question in results")
                        print("Available questions:")
                        for i, result in enumerate(validation_result.get('results', [])):
                            print(f"  {i+1}. {result.get('question', 'No question')}")
                        
                else:
                    print(f"❌ Validation failed: {response.status}")
                    print(await response.text())
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_production_api())
