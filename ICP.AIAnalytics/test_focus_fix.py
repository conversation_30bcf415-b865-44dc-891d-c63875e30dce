#!/usr/bin/env python3
"""
Test script to verify the focus_prompt fix and RAG functionality.
"""

import requests
import json
import time

def test_focus_prompt_fix():
    """Test that the focus_prompt functionality works without errors."""
    
    url = "http://localhost:8000/api/v1/validate"
    
    # Test 1: Without focus_prompt (should work)
    payload_1 = {
        "report_id": "test-report-123",
        "async_processing": False
    }
    
    print("Test 1: Without focus_prompt")
    print(f"Payload: {json.dumps(payload_1, indent=2)}")
    
    try:
        response = requests.post(url, json=payload_1, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test 1 PASSED - No focus_prompt works")
            
            # Check for errors in results
            results = result.get('results', [])
            error_count = sum(1 for r in results if r.get('status') == 'error')
            print(f"Error count: {error_count}/{len(results)}")
            
            if error_count > 0:
                # Show first error
                first_error = next(r for r in results if r.get('status') == 'error')
                print(f"First error: {first_error.get('summary', 'N/A')}")
        else:
            print(f"❌ Test 1 FAILED: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Test 1 FAILED: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: With focus_prompt (should work)
    payload_2 = {
        "report_id": "test-report-123",
        "validation_options": {
            "focus_prompt": "Focus on legal status and credit granting rules"
        },
        "async_processing": False
    }
    
    print("Test 2: With focus_prompt")
    print(f"Payload: {json.dumps(payload_2, indent=2)}")
    
    try:
        response = requests.post(url, json=payload_2, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test 2 PASSED - With focus_prompt works")
            
            # Check for errors in results
            results = result.get('results', [])
            error_count = sum(1 for r in results if r.get('status') == 'error')
            print(f"Error count: {error_count}/{len(results)}")
            
            if error_count > 0:
                # Show first error
                first_error = next(r for r in results if r.get('status') == 'error')
                print(f"First error: {first_error.get('summary', 'N/A')}")
            else:
                print("✅ No errors found - focus_prompt is working!")
                
        else:
            print(f"❌ Test 2 FAILED: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Test 2 FAILED: {e}")

def test_rag_functionality():
    """Test RAG initialization and retrieval."""

    print("Testing RAG functionality...")
    print("="*50)

    # Test 1: Initialize RAG knowledge base
    print("Test 1: Initialize RAG knowledge base")

    rag_url = "http://localhost:8000/api/v1/rag/initialize-from-file"

    try:
        # Upload the examples file
        with open("examples/correct_validation_examples.json", "rb") as f:
            files = {"file": ("correct_validation_examples.json", f, "application/json")}
            response = requests.post(rag_url, files=files, timeout=30)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ RAG initialization successful!")
            print(f"Examples processed: {result.get('examples_processed', 0)}")
        else:
            print(f"❌ RAG initialization failed: {response.text}")

    except Exception as e:
        print(f"❌ RAG initialization failed: {e}")

    print("\n" + "="*50 + "\n")

    # Test 2: Check RAG status
    print("Test 2: Check RAG status")

    status_url = "http://localhost:8000/api/v1/rag/status"

    try:
        response = requests.get(status_url, timeout=10)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            status = response.json()
            print("✅ RAG status check successful!")
            print(f"RAG enabled: {status.get('rag_enabled', False)}")
            if status.get('examples_collection'):
                print(f"Examples count: {status['examples_collection'].get('count', 0)}")
        else:
            print(f"❌ RAG status check failed: {response.text}")

    except Exception as e:
        print(f"❌ RAG status check failed: {e}")

    print("\n" + "="*50 + "\n")

    # Test 3: Test RAG retrieval
    print("Test 3: Test RAG retrieval")

    retrieval_url = "http://localhost:8000/api/v1/rag/test-retrieval"
    test_query = {
        "question": "Company name should match requested name",
        "xml_content": "<CompanyName>Test Corp</CompanyName><Requested>Test Corp</Requested>"
    }

    try:
        response = requests.post(retrieval_url, json=test_query, timeout=30)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ RAG retrieval test successful!")
            retrieved_data = result.get('retrieved_data', {})
            examples = retrieved_data.get('examples', [])
            patterns = retrieved_data.get('patterns', [])
            print(f"Examples retrieved: {len(examples)}")
            print(f"Patterns retrieved: {len(patterns)}")

            if examples:
                print(f"Top example relevance: {examples[0].get('relevance_score', 0):.2f}")
        else:
            print(f"❌ RAG retrieval test failed: {response.text}")

    except Exception as e:
        print(f"❌ RAG retrieval test failed: {e}")

if __name__ == "__main__":
    print("Testing focus_prompt fix and RAG functionality...")
    print("="*60)

    # Test focus prompt functionality
    test_focus_prompt_fix()

    print("\n" + "="*60 + "\n")

    # Test RAG functionality
    test_rag_functionality()

    print("\nAll tests completed!")
