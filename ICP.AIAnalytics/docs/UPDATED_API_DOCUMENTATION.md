# Updated Enhanced Validation API Documentation

## Single Bearer Token Implementation ✅

The validation API has been enhanced with a **single bearer token design** that eliminates confusion and provides a cleaner, more intuitive API.

### Endpoint: POST `/api/v1/validate`

**Two modes of operation:**
1. **Traditional Mode**: Validate existing reports (no `bearer_token`)
2. **Upload & Validate Mode**: Upload from external API + validate (with `bearer_token`)

## ✅ **New Request Schema (Single Bearer Token)**

```json
{
  "report_id": "string",              // Required: Report ID
  "bearer_token": "string",           // Optional: Single token for ALL operations
  "validation_options": {},           // Optional: Validation configuration
  "enable_client_filtering": true,    // Optional: Enable client-specific filtering
  "order_details_params": {           // Optional: Order details (NO bearer_token here)
    "csr_id": "string",
    "copy": "string",
    "version": "string"
  },
  "direct_client_code": "string"      // Optional: Direct client code
}
```

### 🔑 **Bearer Token Usage**

The **single `bearer_token`** is used for:
- ✅ Uploading report from external API
- ✅ Calling order details API (when needed)
- ✅ All external API authentication

## 📝 **Usage Examples**

### **1. Traditional Validation (No Upload)**
```bash
curl -X POST "/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "direct_client_code": "ONLINEMISC"
  }'
```

### **2. Upload & Validate with Direct Client Code**
```bash
curl -X POST "/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "bearer_token": "your_token_here",
    "direct_client_code": "ONLINEMISC"
  }'
```

### **3. Upload & Validate with Order Details API**
```bash
curl -X POST "/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493", 
    "bearer_token": "your_token_here",
    "enable_client_filtering": true,
    "order_details_params": {
      "csr_id": "12345",
      "copy": "1", 
      "version": "2"
    }
  }'
```

### **4. Async Upload & Validate**
```bash
curl -X POST "/api/v1/validate?async_processing=true" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "bearer_token": "your_token_here",
    "direct_client_code": "ONLINEMISC"
  }'
```

## 📤 **Response Examples**

### **Synchronous Response (with upload)**
```json
{
  "validation_id": "uuid",
  "status": "completed",
  "report_id": "181493",
  "total_questions": 41,
  "processed_questions": 38,
  "skipped_questions": 3,
  "results": [...],
  "validation_timestamp": "2025-07-11T12:29:31.982485",
  "processing_time": 15.2,
  "upload_info": {
    "file_id": "uuid",
    "filename": "report_181493_xml.xml",
    "file_type": ".xml",
    "file_size": 2320,
    "upload_timestamp": "2025-07-11T12:29:30.000000",
    "status": "uploaded",
    "source": "external_api",
    "report_id": "181493"
  }
}
```

### **Asynchronous Response (with upload)**
```json
{
  "validation_id": "uuid",
  "status": "processing",
  "message": "Validation started in background (report uploaded)",
  "check_status_url": "/api/v1/validate/status/uuid",
  "using_permanent_question_bank": true,
  "upload_info": {
    "file_id": "uuid",
    "filename": "report_181493_xml.xml",
    "status": "uploaded",
    "source": "external_api"
  }
}
```

## 🎯 **Key Improvements**

### **Before (Confusing)**
```json
{
  "bearer_token": "token1",           // ❌ For upload
  "order_details_params": {
    "bearer_token": "token2"          // ❌ For order details
  }
}
```

### **After (Clean)**
```json
{
  "bearer_token": "single_token",     // ✅ For ALL operations
  "order_details_params": {
    // ✅ No bearer_token here
  }
}
```

## ✨ **Benefits**

1. **Single Source of Truth**: One token for all external API operations
2. **Simplified Integration**: No confusing duplicate fields
3. **Better User Experience**: Users provide one authentication token
4. **Cleaner Code**: Less parameter management
5. **Standard Design**: Follows API best practices
6. **Backward Compatible**: Existing calls work unchanged

## 🔄 **Status Endpoints**

All existing status endpoints work unchanged:
- `GET /api/v1/validate/status/{validation_id}`
- `GET /api/v1/validate/result/{validation_id}`
- `GET /api/v1/validate/summary/{validation_id}`

## ⚡ **Performance**

- **Single API Call**: Upload + validate in one request
- **Atomic Operations**: Both succeed or both fail
- **Reduced Latency**: No intermediate API calls
- **Consistent Error Handling**: Single point of failure management

## 🔒 **Security**

- Single authentication point
- Consistent token validation
- No token duplication risks
- Clear audit trail

## 🧪 **Testing**

Use the provided test script:
```bash
python test_single_bearer_token.py
```

The test demonstrates all scenarios and shows the improved request structure.

---

**Migration Note**: Existing integrations continue to work without changes. The single bearer token design is available immediately for new integrations.
