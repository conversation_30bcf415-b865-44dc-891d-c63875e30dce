#!/usr/bin/env python3
"""
Test script for the new focus_prompt feature in validation API.
This script tests the ability to regenerate reports with specific focus using frontend input.
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:8000"
REPORT_ID = "RPT-2024-001"  # Replace with actual report ID
BEARER_TOKEN = "your-bearer-token"  # Replace with actual token
CLIENT_CODE = "TURKEXIM"  # Replace with actual client code

def test_validation_with_focus_prompt():
    """Test validation API with focus_prompt parameter."""
    print("=== Testing Validation with Focus Prompt ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    
    # Test case 1: Focus on payment-related sections
    payload_1 = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,
        "direct_client_code": CLIENT_CODE,
        "async_processing": False,
        "validation_options": {
            "focus_prompt": "Focus on payment-related sections and analyze currency compliance in detail"
        }
    }
    
    print(f"Test 1 - Payment Focus:")
    print(f"Request: {json.dumps(payload_1, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload_1)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Processing Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Processed Questions: {result.get('processed_questions')}")
        
        # Show first few results to see if focus was applied
        results = result.get('results', [])
        if results:
            print("\nFirst 3 validation results:")
            for i, res in enumerate(results[:3]):
                print(f"  {i+1}. Question: {res.get('question', '')[:80]}...")
                print(f"     Summary: {res.get('summary', '')}")
                print(f"     Status: {res.get('status', '')}")
                print()
    else:
        print(f"Error: {response.text}")
    
    print("\n" + "="*60 + "\n")
    
    # Test case 2: Focus on company information
    payload_2 = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,
        "direct_client_code": CLIENT_CODE,
        "async_processing": False,
        "validation_options": {
            "focus_prompt": "Examine company name and registration details more thoroughly, check for any discrepancies"
        }
    }
    
    print(f"Test 2 - Company Information Focus:")
    print(f"Request: {json.dumps(payload_2, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload_2)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Processing Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Processed Questions: {result.get('processed_questions')}")
        
        # Show first few results to see if focus was applied
        results = result.get('results', [])
        if results:
            print("\nFirst 3 validation results:")
            for i, res in enumerate(results[:3]):
                print(f"  {i+1}. Question: {res.get('question', '')[:80]}...")
                print(f"     Summary: {res.get('summary', '')}")
                print(f"     Status: {res.get('status', '')}")
                print()
    else:
        print(f"Error: {response.text}")

def test_validation_without_focus_prompt():
    """Test validation API without focus_prompt for comparison."""
    print("=== Testing Validation without Focus Prompt (Control) ===")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": REPORT_ID,
        "bearer_token": BEARER_TOKEN,
        "direct_client_code": CLIENT_CODE,
        "async_processing": False
        # No focus_prompt field
    }
    
    print(f"Request: {json.dumps(payload, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload)
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Processing Time: {end_time - start_time:.2f}s")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Validation ID: {result.get('validation_id')}")
        print(f"Total Questions: {result.get('total_questions')}")
        print(f"Processed Questions: {result.get('processed_questions')}")
        
        # Show first few results for comparison
        results = result.get('results', [])
        if results:
            print("\nFirst 3 validation results (no focus):")
            for i, res in enumerate(results[:3]):
                print(f"  {i+1}. Question: {res.get('question', '')[:80]}...")
                print(f"     Summary: {res.get('summary', '')}")
                print(f"     Status: {res.get('status', '')}")
                print()
    else:
        print(f"Error: {response.text}")

if __name__ == "__main__":
    print("Focus Prompt Feature Test")
    print("=" * 50)
    print("This test validates the new focus_prompt feature")
    print("Make sure to update REPORT_ID, BEARER_TOKEN, and CLIENT_CODE")
    print("=" * 50)
    
    # Test without focus prompt first (control)
    test_validation_without_focus_prompt()
    
    print("\n" + "="*60 + "\n")
    
    # Test with focus prompts
    test_validation_with_focus_prompt()
    
    print("\nTest completed!")
