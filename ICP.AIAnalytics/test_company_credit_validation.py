#!/usr/bin/env python3
"""
Test script to validate the company size vs credit amount validation rules.
This test simulates the API request and adds validation rules for:
1. Large company with small credit amount (mark as issue)
2. Small company with large credit amount (mark as issue)
"""

import asyncio
import json
import sys
import os
import httpx

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.validation_service import ValidationService
from app.models.schemas import Question

# Test request body (as provided by user)
TEST_REQUEST_BODY = {
    "report_id": "1981352",
    "validation_options": {},
    "enable_client_filtering": True,
    "order_details_params": {
        "csr_id": "91185136",
        "copy": "2",
        "version": "1"
    },
    "direct_client_code": "",
    "bearer_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._mgdJENXO0YnVyA0gjBUwNzSUQzSrtCONUzwL5kjJHQ"
}

# Test XML content with company size and credit information
TEST_XML_CONTENT = """<?xml version="1.0" encoding="UTF-8"?>
<Report>
    <HeaderSection>
        <CompanyName>MegaCorp International Ltd</CompanyName>
        <Requested>MegaCorp International Ltd</Requested>
    </HeaderSection>
    <CompanyInformation>
        <CompanySize>Large</CompanySize>
        <NumberOfEmployees>5000</NumberOfEmployees>
        <AnnualTurnover>
            <Amount>50000000</Amount>
            <Currency>EUR</Currency>
        </AnnualTurnover>
        <CompanyType>Public Limited Company</CompanyType>
    </CompanyInformation>
    <CreditInformation>
        <MaxCredit>
            <Amount>30000</Amount>
            <Currency>EUR</Currency>
        </MaxCredit>
        <CreditLimit>
            <Amount>25000</Amount>
            <Currency>GBP</Currency>
        </CreditLimit>
        <ExchangeRates>
            <Rate>
                <FromCurrency>EUR</FromCurrency>
                <ToCurrency>GBP</ToCurrency>
                <Rate>0.86</Rate>
            </Rate>
            <Rate>
                <FromCurrency>USD</FromCurrency>
                <ToCurrency>GBP</ToCurrency>
                <Rate>0.79</Rate>
            </Rate>
        </ExchangeRates>
    </CreditInformation>
    <FinancialInformation>
        <TotalIncome>
            <Amount>50000000</Amount>
            <Currency>EUR</Currency>
        </TotalIncome>
        <GrossProfit>
            <Amount>10000000</Amount>
            <Currency>EUR</Currency>
        </GrossProfit>
    </FinancialInformation>
</Report>"""

# Test questions for company size vs credit validation
COMPANY_CREDIT_VALIDATION_QUESTIONS = [
    {
        "id": "company-credit-mismatch-1",
        "question": "Mark as an issue if a large company is associated with a small credit amount (convert to GBP and compare with thresholds)",
        "darwin_reference_sections": "(Company) Company Size, (Credit) Max Credit, (Financial) Credit Information",
        "expected_outcome": "rejected",
        "client_specific_type": "All",
        "description": "Large company with small credit - should be flagged as issue"
    },
    {
        "id": "company-credit-mismatch-2", 
        "question": "Mark as an issue if a small company is associated with a large credit amount (convert to GBP and compare with thresholds)",
        "darwin_reference_sections": "(Company) Company Size, (Credit) Max Credit, (Financial) Credit Information",
        "expected_outcome": "rejected",
        "client_specific_type": "All",
        "description": "Small company with large credit - should be flagged as issue"
    },
    {
        "id": "company-credit-classification-1",
        "question": "Classify the company size and credit amount, then determine if there's a mismatch between company size and credit classification",
        "darwin_reference_sections": "(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit",
        "expected_outcome": "rejected",
        "client_specific_type": "All", 
        "description": "Comprehensive company-credit analysis"
    }
]

async def test_company_credit_validation():
    """Test the company size vs credit amount validation functionality."""
    print("🧪 Testing Company Size vs Credit Amount Validation")
    print("=" * 70)
    
    try:
        # Initialize validation service
        validation_service = ValidationService()
        
        # Test each validation question
        for i, test_case in enumerate(COMPANY_CREDIT_VALIDATION_QUESTIONS, 1):
            print(f"\n📋 Test Case {i}: {test_case['description']}")
            print(f"Question: {test_case['question']}")
            print(f"Expected Outcome: {test_case['expected_outcome']}")
            
            # Create Question object
            question = Question(
                id=test_case['id'],
                question=test_case['question'],
                darwin_reference_sections=test_case['darwin_reference_sections'],
                expected_outcome=test_case['expected_outcome'],
                client_specific_type=test_case['client_specific_type']
            )
            
            try:
                # Generate validation response
                if validation_service.llm:
                    response = await validation_service._generate_enhanced_llm_response(
                        question=question,
                        xml_content=TEST_XML_CONTENT,
                        question_number=i
                    )
                    
                    # Parse the response
                    parsed_response = validation_service._parse_llm_response(response)
                    
                    print(f"✅ LLM Response:")
                    print(f"   Summary: {parsed_response.get('summary', 'N/A')}")
                    print(f"   Status: {parsed_response.get('status', 'N/A')}")
                    print(f"   Confidence: {parsed_response.get('confidence_score', 'N/A')}")
                    print(f"   Reasoning: {parsed_response.get('reasoning', 'N/A')}")
                    
                    # Check if the response correctly identifies the mismatch
                    summary = parsed_response.get('summary', '').lower()
                    status = parsed_response.get('status', '')
                    
                    # For this test case (large company with small credit), we expect it to be flagged
                    if i == 1:  # Large company with small credit
                        if 'issue' in summary or 'mismatch' in summary or status == 'rejected':
                            print(f"✅ PASS: Correctly identified large company with small credit as an issue")
                        else:
                            print(f"❌ FAIL: Did not identify the company-credit mismatch")
                    
                    elif i == 3:  # Comprehensive analysis
                        if any(keyword in summary for keyword in ['large', 'small', 'mismatch', 'issue']):
                            print(f"✅ PASS: Provided comprehensive company-credit analysis")
                        else:
                            print(f"❌ FAIL: Did not provide adequate company-credit analysis")
                
                else:
                    print("⚠️  No LLM available - using fallback response")
                    fallback_response = validation_service._generate_fallback_response(question, TEST_XML_CONTENT)
                    parsed_fallback = json.loads(fallback_response)
                    print(f"   Fallback Summary: {parsed_fallback.get('summary', 'N/A')}")
                    
            except Exception as e:
                print(f"❌ Error in test case {i}: {str(e)}")
                
        print("\n" + "=" * 70)
        print("🏁 Company Credit Validation Test Complete")
        
    except Exception as e:
        print(f"❌ Error initializing validation service: {str(e)}")
        return False
    
    return True

async def test_api_endpoint():
    """Test the actual API endpoint with the provided request body."""
    print("\n🌐 Testing API Endpoint with Provided Request")
    print("=" * 70)
    
    try:
        # Test the API endpoint (assuming it's running on localhost:8000)
        api_url = "http://localhost:8000/api/v1/validate"
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(api_url, json=TEST_REQUEST_BODY)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ API Response received successfully")
                    print(f"   Validation ID: {result.get('validation_id', 'N/A')}")
                    print(f"   Status: {result.get('status', 'N/A')}")
                    print(f"   Total Questions: {result.get('total_questions', 'N/A')}")
                    print(f"   Processed Questions: {result.get('processed_questions', 'N/A')}")
                    
                    # Look for company-credit related validations
                    results = result.get('results', [])
                    company_credit_results = []
                    
                    for res in results:
                        question = res.get('question', '').lower()
                        summary = res.get('summary', '').lower()
                        
                        if any(keyword in question or keyword in summary 
                               for keyword in ['company', 'credit', 'size', 'large', 'small', 'mismatch']):
                            company_credit_results.append(res)
                    
                    if company_credit_results:
                        print(f"\n📊 Found {len(company_credit_results)} company-credit related validations:")
                        for i, res in enumerate(company_credit_results, 1):
                            print(f"   {i}. Question: {res.get('question', 'N/A')[:80]}...")
                            print(f"      Summary: {res.get('summary', 'N/A')}")
                            print(f"      Status: {res.get('status', 'N/A')}")
                    else:
                        print("⚠️  No company-credit related validations found in current question bank")
                        
                else:
                    print(f"❌ API request failed with status {response.status_code}")
                    print(f"   Response: {response.text}")
                    
            except httpx.ConnectError:
                print("⚠️  Could not connect to API endpoint (server may not be running)")
                print("   To test the API endpoint, start the server with: python start.py")
                
    except Exception as e:
        print(f"❌ Error testing API endpoint: {str(e)}")
        return False
    
    return True

async def main():
    """Run all company credit validation tests."""
    print("🚀 Starting Company Size vs Credit Amount Validation Tests")
    print("=" * 70)
    
    # Test validation logic
    validation_test_passed = await test_company_credit_validation()
    
    # Test API endpoint
    api_test_passed = await test_api_endpoint()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 70)
    print(f"Validation Logic Test: {'✅ PASSED' if validation_test_passed else '❌ FAILED'}")
    print(f"API Endpoint Test: {'✅ PASSED' if api_test_passed else '❌ FAILED'}")
    
    if validation_test_passed:
        print("\n🎉 Company-credit validation logic is working correctly!")
        print("\n📝 Next Steps:")
        print("1. Add the company-credit validation questions to your permanent question bank")
        print("2. Update the question bank Excel file with the new validation rules")
        print("3. Test with real report data to ensure proper currency conversion")
        return True
    else:
        print("\n⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
